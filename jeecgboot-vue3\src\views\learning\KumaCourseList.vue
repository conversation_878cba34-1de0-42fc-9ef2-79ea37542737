<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'learning:kuma_course:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'learning:kuma_course:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'learning:kuma_course:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'learning:kuma_course:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
        <a-image v-if="column.key === 'avatar'" :size="60" :src="record.avatar" />
      </template>
      <template #img="{ text }">
         <a-image :size="60" :src="text" />
      </template>
      <template #price="{ record }">
         {{record.price}} / {{record.originalPrice}} 
      </template>
      <template #level="{ text }">
         <span v-if="text === 'beginner'">初级</span>
         <span v-if="text === 'intermediate'">中级</span>
         <span v-if="text === 'advanced'">高级</span>
         <span v-if="text === 'professional'">专业级</span>
      </template>
      <template #status="{ text }">
         <a-tag v-if="text === 0 || text === '0'" color="red">下架</a-tag>
         <a-tag v-if="text === 1 || text === '1'" color="green">上架</a-tag>
      </template>
      <template #isRecommend="{ text }">
         <a-tag v-if="text === 0 || text === '0'" color="default">否</a-tag>
         <a-tag v-if="text === 1 || text === '1'" color="blue">是</a-tag>
      </template>
      <template #isHot="{ text }">
         <a-tag v-if="text === 0 || text === '0'" color="default">否</a-tag>
         <a-tag v-if="text === 1 || text === '1'" color="orange">是</a-tag>
      </template>
      <template #title="{ record }">
        <a @click="handleGotoLesson(record.id)" style="cursor: pointer; color: #1890ff;">{{ record.title || '课程标题' }}</a>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <KumaCourseModal @register="registerModal" @success="handleSuccess"></KumaCourseModal>
  </div>
</template>

<script lang="ts" name="learning-kumaCourse" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction,TableImg} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import KumaCourseModal from '/@/components/learning/KumaCourseModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './KumaCourse.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './KumaCourse.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  // import { useGo } from '/@/hooks/web/usePage';
    import { Image as AImage, Tag as ATag } from 'ant-design-vue';
    import { router } from '/@/router';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  // const go = useGo();
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: 'kuma_course',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
      },
       exportConfig: {
            name:"kuma_course",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 跳转到课程详情页面
    */
   function handleGotoLesson(courseId: string) {
     //go(`/learning/kuma_lesson?courseId=${courseId}`);

     router.push('/learning/kumaLessonList?courseId='+courseId)
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'learning:kuma_course:edit'
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'learning:kuma_course:delete'
         }
       ]
   }


</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>