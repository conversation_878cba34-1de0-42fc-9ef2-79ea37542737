import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { UploadTypeEnum } from '/@/components/Form/src/jeecg/components/JUpload';
import { values } from 'xe-utils';
import { options } from '../monitor/mynews/XssWhiteList';
//列表数据
export const columns: BasicColumn[] = [
  //  {
  //   title: '系统标识',
  //   align:"center",
  //   dataIndex: 'sysSign'
  //  },
   {
    title: '课程标题',
    align:"center",
    dataIndex: 'title',
    slots: { customRender: 'title' },
   },
   {
    title: '课程描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '课程封面',
    align:"center",
    dataIndex: 'cover',
    slots: { customRender: 'img' },
   },
  //  {
  //   title: '课程分类ID',
  //   align:"center",
  //   dataIndex: 'categoryId'
  //  },
   {
    title: '课程难度',
    align:"center",
    dataIndex: 'level',
    slots: { customRender: 'level' },
    width:100
   },
   {
    title: '课程价格',
    align:"center",
    dataIndex: 'price',
    slots: { customRender: 'price' },
   },
  //  {
  //   title: '原价',
  //   align:"center",
  //   dataIndex: 'originalPrice'
  //  },
   {
    title: '课程总时长',
    align:"center",
    dataIndex: 'duration',
    width:80
   },
   {
    title: '学习人数',
    align:"center",
    dataIndex: 'studentCount',
    width:80
   },
   {
    title: '课时数量',
    align:"center",
    dataIndex: 'lessonCount',
    width:80
   },
   {
    title: '上架状态',
    align:"center",
    dataIndex: 'status',
    slots: { customRender: 'status' },
    width:80
   },
   {
    title: '是否推荐',
    align:"center",
    dataIndex: 'isRecommend',
    slots: { customRender: 'isRecommend' },
    width:80
   },
   {
    title: '是否热门',
    align:"center",
    dataIndex: 'isHot',
    slots: { customRender: 'isHot' },
    width:80
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  // {
  //   label: '系统标识',
  //   field: 'sysSign',
  //   component: 'Input',
  //   dynamicRules: ({model,schema}) => {
  //         return [
  //                { required: true, message: '请输入系统标识!'},
  //         ];
  //    },
  // },
  {
    label: '课程标题',
    field: 'title',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程标题!'},
          ];
     },
  },
  {
    label: '课程描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '课程封面',
    field: 'cover',
    component: 'JUpload',
     helpMessage: '最多上传1张图片',
     componentProps: {
      fileType: UploadTypeEnum.image,
      maxCount: 1,
    },
  },
  // {
  //   label: '课程分类ID',
  //   field: 'categoryId',
  //   component: 'Input',
  //   dynamicRules: ({model,schema}) => {
  //         return [
  //                { required: true, message: '请输入课程分类ID!'},
  //         ];
  //    },
  // },
  {
    label: '课程难度级别',
    field: 'level',
    component: 'Select',
    componentProps: {
      options: [
        { value: 'beginner', label: '入门' },
        { value: 'intermediate', label: '中级' },
        { value: 'advanced', label: '高级' },
        { value: 'professional', label: '专业级' },
      ],
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程难度级别：beginner/intermediate/advanced/professional!'},
          ];
     },
  },
  {
    label: '课程价格',
    field: 'price',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程价格!'},
          ];
     },
  },
  {
    label: '原价',
    field: 'originalPrice',
    component: 'InputNumber',
  },
  {
    label: '课程总时长(分钟)',
    field: 'duration',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程总时长(分钟)!'},
          ];
     },
  },
  // {
  //   label: '学习人数',
  //   field: 'studentCount',
  //   component: 'InputNumber',
  //   dynamicRules: ({model,schema}) => {
  //         return [
  //                { required: true, message: '请输入学习人数!'},
  //         ];
  //    },
  // },
  {
    label: '课时数量',
    field: 'lessonCount',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课时数量!'},
          ];
     },
  },
  {
    label: '上架状态',
    field: 'status',
     component: 'JSwitch',
     componentProps: {
      options: ['1', '0'], // Fixed: Swapped order to match JSwitch logic
      labelOptions: ['上架', '下架'], // Added labels for clarity
    },
    // Value transformation to ensure consistency
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入状态：0-下架 1-上架!'},
          ];
     },
  },
  {
    label: '是否推荐',
    field: 'isRecommend',
     component: 'JSwitch',
     componentProps : {
      options: ['1', '0'], // Fixed: Swapped order to match JSwitch logic
      labelOptions: ['是', '否'], // Added labels for clarity
    },
    // dynamicRules: ({model,schema}) => {
    //       return [
    //              { required: true, message: '请输入是否推荐：0-否 1-是!'},
    //       ];
    //  },
  },
  {
    label: '是否热门',
    field: 'isHot',
     component: 'JSwitch',
     componentProps: {
      options: ['1', '0'], // Fixed: Swapped order to match JSwitch logic
      labelOptions: ['是', '否'], // Added labels for clarity
    },
    // dynamicRules: ({model,schema}) => {
    //       return [
    //              { required: true, message: '请输入是否热门：0-否 1-是!'},
    //       ];
    //  },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  // sysSign: {title: '系统标识',order: 0,view: 'text', type: 'string',},
  title: {title: '课程标题',order: 1,view: 'text', type: 'string',},
  description: {title: '课程描述',order: 2,view: 'textarea', type: 'string',},
  cover: {title: '课程封面图片URL',order: 3,view: 'text', type: 'string',},
  categoryId: {title: '课程分类ID',order: 4,view: 'text', type: 'string',},
  level: {title: '课程难度级别：beginner/intermediate/advanced',order: 5,view: 'text', type: 'string',},
  price: {title: '课程价格',order: 6,view: 'number', type: 'number',},
  originalPrice: {title: '原价',order: 7,view: 'number', type: 'number',},
  duration: {title: '课程总时长(分钟)',order: 8,view: 'number', type: 'number',},
  studentCount: {title: '学习人数',order: 9,view: 'number', type: 'number',},
  lessonCount: {title: '课时数量',order: 10,view: 'number', type: 'number',},
  status: {title: '状态：0-下架 1-上架',order: 11,view: 'number', type: 'number',},
  isRecommend: {title: '是否推荐：0-否 1-是',order: 12,view: 'number', type: 'number',},
  isHot: {title: '是否热门：0-否 1-是',order: 13,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}