import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [

   {
    title: '课时标题',
    align:"center",
    dataIndex: 'title'
   },
   {
    title: '课时描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '课程内容',
    align:"center",
    dataIndex: 'lessonText'
   },
   {
    title: '课时时长(分钟)',
    align:"center",
    dataIndex: 'duration'
   },
   {
    title: '上架状态',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  // {
  //   label: '系统标识',
  //   field: 'sysSign',
  //   component: 'Input',
  //   dynamicRules: ({model,schema}) => {
  //         return [
  //                { required: true, message: '请输入系统标识!'},
  //         ];
  //    },
  // },
  // {
  //   label: '所属课程ID',
  //   field: 'courseId',
  //   component: 'Input',
  //   dynamicRules: ({model,schema}) => {
  //         return [
  //                { required: true, message: '请输入所属课程ID!'},
  //         ];
  //    },
  // },
  {
    label: '课时标题',
    field: 'title',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课时标题!'},
          ];
     },
  },
  {
    label: '课时描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '课时内容',
    field: 'description',
    component: 'JEditor',
  },
  {
    label: '视频URL',
    field: 'videoUrl',
    component: 'JUpload',
  },
  {
    label: '课时时长(分钟)',
    field: 'duration',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课时时长(分钟)!'},
          ];
     },
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入排序!'},
          ];
     },
  },
  {
    label: '上架状态',
    field: 'status',
    component: 'JSwitch',
    componentProps: {
      options: ['0', '1'],
    },
    
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sysSign: {title: '系统标识',order: 0,view: 'text', type: 'string',},
  courseId: {title: '所属课程ID',order: 1,view: 'text', type: 'string',},
  title: {title: '课时标题',order: 2,view: 'text', type: 'string',},
  description: {title: '课时描述',order: 3,view: 'textarea', type: 'string',},
  videoUrl: {title: '视频URL',order: 4,view: 'text', type: 'string',},
  duration: {title: '课时时长(分钟)',order: 5,view: 'number', type: 'number',},
  sort: {title: '排序',order: 6,view: 'number', type: 'number',},
  status: {title: '状态：0-禁用 1-启用',order: 7,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}